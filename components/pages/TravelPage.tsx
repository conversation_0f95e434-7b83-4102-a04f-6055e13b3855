import MapLinks from '@/components/MapLinks';

export default function TravelPage() {
  return (
    <div className="w-full max-w-[600px] mx-auto text-center text-white pt-6 pb-12 px-2 md:px-0">
      <h1 className="text-4xl md:text-6xl mb-8 font-league-gothic">Travel</h1>
      <div className="space-y-8 text-lg md:text-xl">
        <p>
          Planning your trip to Forever Fest 2026? Here's everything you need to know.
        </p>

        {/* Wedding Venue Address & Map Links */}
        <section className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-left">
          <h2 className="text-2xl mb-4 font-league-gothic text-center">Wedding Venue</h2>
          <div className="text-center mb-4">
            <p className="text-xl mb-2">DEC on Dragon</p>
            <p className="text-lg">1414 Dragon St, Dallas, TX 75207</p>
          </div>
          <div className="flex justify-center">
            <MapLinks />
          </div>
        </section>

        {/* Getting to Dallas by Air */}
        <section className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-left">
          <h2 className="text-2xl mb-4 font-league-gothic text-center">Getting to Dallas by Air</h2>
          <p>Flight information and airport details coming soon...</p>
        </section>

        {/* Getting to the Venue by Car */}
        <section className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-left">
          <h2 className="text-2xl mb-4 font-league-gothic text-center">Getting to the Venue by Car</h2>
          <p>Driving directions and parking information coming soon...</p>
        </section>

        {/* Getting Around Dallas */}
        <section className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-left">
          <h2 className="text-2xl mb-4 font-league-gothic text-center">Getting Around Dallas</h2>

          <div className="space-y-4">
            <div>
              <h3 className="text-xl mb-2 font-league-gothic">Ride Sharing</h3>
              <p>Uber and Lyft information coming soon...</p>
            </div>

            <div>
              <h3 className="text-xl mb-2 font-league-gothic">Rental Cars</h3>
              <p>Car rental options and recommendations coming soon...</p>
            </div>

            <div>
              <h3 className="text-xl mb-2 font-league-gothic">Public Transit</h3>
              <p>DART and public transportation information coming soon...</p>
            </div>
          </div>
        </section>

        {/* Hotel Block Details */}
        <section className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-left">
          <h2 className="text-2xl mb-4 font-league-gothic text-center">Hotel Block Details</h2>
          <p>Hotel recommendations and booking information coming soon...</p>
        </section>
      </div>
    </div>
  );
}
